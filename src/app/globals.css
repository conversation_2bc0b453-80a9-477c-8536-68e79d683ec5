@tailwind base;
@tailwind components;
@tailwind utilities;

/* Add the extracted CSS below */
@layer components {
:root {
    /* Color Variables */
    --primary: #4f46e5; /* Indigo-600 */
    --primary-light: #818cf8; /* Indigo-400 */
    --primary-dark: #4338ca; /* Indigo-700 */
    --secondary: #0ea5e9; /* Sky-500 */
    --accent: #f59e0b; /* Amber-500 */
    --neutral-50: #f9fafb; /* Gray-50 */
    --neutral-100: #f3f4f6; /* Gray-100 */
    --neutral-200: #e5e7eb; /* Gray-200 */
    --neutral-300: #d1d5db; /* Gray-300 */
    --neutral-400: #9ca3af; /* Gray-400 */
    --neutral-500: #6b7280; /* Gray-500 */
    --neutral-600: #4b5563; /* Gray-600 */
    --neutral-700: #374151; /* Gray-700 */
    --neutral-800: #1f2937; /* Gray-800 */
    --neutral-900: #111827; /* Gray-900 */
    --success: #10b981; /* Emerald-500 */
    --danger: #ef4444; /* Red-500 */
    --info: #3b82f6; /* Blue-500 */

    /* Transition Variables - Standardized for consistency */
    --transition-duration: 0.3s;
    --transition-timing: ease;
    --transition-theme: background-color var(--transition-duration) var(--transition-timing),
                        color var(--transition-duration) var(--transition-timing),
                        border-color var(--transition-duration) var(--transition-timing),
                        box-shadow var(--transition-duration) var(--transition-timing);
    --transition-interactive: all 0.15s ease-in-out; /* For buttons and interactive elements */
    --transition-transform: transform 0.3s var(--transition-timing);
}
html { scroll-behavior: smooth; }
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--neutral-100);
    color: var(--neutral-800);
    transition: var(--transition-theme);
}
header { transition: var(--transition-theme); }
/* Modern Split Layout Styles */
@media (min-width: 1024px) {
    .split-layout {
        display: flex;
        gap: 1.5rem;
    }

    .split-layout-main {
        width: 66.666667%;
    }

    .split-layout-sidebar {
        width: 33.333333%;
    }
}

/* Card Styles */
.card-container {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid var(--neutral-200);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
    height: 100%;
    transition: all 0.2s ease;
    position: relative;
    max-width: 100%; /* Ensure card doesn't exceed its container */
    overflow: hidden; /* Prevent content from overflowing */
}

.card-container:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.card-container.dragging {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    opacity: 0.9;
    transform: scale(1.02);
    cursor: grabbing !important;
    z-index: 1000;
}

.drag-handle {
    opacity: 0;
    transition: opacity 0.2s ease;
    cursor: grab;
}

.card-container:hover .drag-handle {
    opacity: 1;
}

/* Default positioning for cards on desktop - only applied when dragging */
@media (min-width: 768px) {
    .dragging[data-draggable-id="innerText-jobSpecs"],
    .dragging[data-draggable-id="cover-specs"],
    .dragging[data-draggable-id="endpapers-specs"],
    .dragging[data-draggable-id="innerText-prodParams"],
    .dragging[data-draggable-id="cover-prodParams"],
    .dragging[data-draggable-id="endpapers-prodParams"],
    .dragging[data-draggable-id="innerText-unitConverter"],
    .dragging[data-draggable-id="cover-unitConverter"],
    .dragging[data-draggable-id="endpapers-unitConverter"] {
        position: absolute;
        z-index: 1000;
    }
}

body.dark-mode .card-container {
    background-color: var(--neutral-800);
    border-color: var(--neutral-700);
}

/* Unit Converter Card Styles */
.unit-converter-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Card Styles */
.card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--neutral-200);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: var(--transition-theme);
    display: flex;
    flex-direction: column;
    height: 100%; /* Ensure full height */
}
.card-header {
    border-bottom: 1px solid var(--neutral-200);
    padding-bottom: 0.75rem;
    margin-bottom: 1rem;
    transition: var(--transition-theme);
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button { -webkit-appearance: none; margin: 0; }
input[type=number] { -moz-appearance: textfield; appearance: textfield; }
.form-input {
    width: 100%; padding: 0.5rem 0.75rem; border: 1px solid var(--neutral-300);
    border-radius: 0.375rem; font-size: 0.875rem;
    transition: var(--transition-theme);
    box-sizing: border-box; background-color: white; color: var(--neutral-800);
    max-width: 100%; /* Ensure inputs don't exceed their container */
}
.form-select {
    width: 100%; padding: 0.5rem 0.75rem; border: 1px solid var(--neutral-300);
    border-radius: 0.375rem; font-size: 0.875rem; box-sizing: border-box;
    font-family: inherit; line-height: inherit; color: inherit; background-color: white;
    transition: var(--transition-theme);
    -webkit-appearance: none; -moz-appearance: none; appearance: none;
    padding-right: 2.5rem;
    max-width: 100%; /* Ensure selects don't exceed their container */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z' clip-rule='evenodd' /%3E%3C/svg%3E");
    background-repeat: no-repeat; background-position: right 0.7rem center; background-size: 1.25em 1.25em;
}
.form-input::placeholder { color: var(--neutral-400); font-size: 0.8rem; transition: var(--transition-theme); }
.form-input:focus, .form-select:focus {
    border-color: var(--primary); box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2); outline: none;
}
.input-error { border-color: var(--danger) !important; box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2); }
.form-label {
    display: block; font-size: 0.875rem; font-weight: 500;
    color: var(--neutral-700); margin-bottom: 0.375rem; transition: var(--transition-theme);
}
.conversion-icon {
    flex-shrink: 0; color: var(--secondary); transition: var(--transition-theme), transform var(--transition-duration) var(--transition-timing);
    padding: 0.25rem; border-radius: 0.375rem; background-color: var(--neutral-100);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.conversion-icon:hover { background-color: var(--neutral-200); transform: scale(1.05); }
body.dark-mode .conversion-icon { color: var(--neutral-500); background-color: var(--neutral-800); }
body.dark-mode .conversion-icon:hover { background-color: var(--neutral-700); }


/* Custom scrollbar styles */
::-webkit-scrollbar { width: 10px; height: 10px; }
::-webkit-scrollbar-track { background: var(--neutral-200); }
::-webkit-scrollbar-thumb { background-color: var(--neutral-400); border-radius: 5px; }
body.dark-mode { scrollbar-color: var(--neutral-600) var(--neutral-800); scrollbar-width: thin; }
body.dark-mode ::-webkit-scrollbar-track { background: var(--neutral-800); }
body.dark-mode ::-webkit-scrollbar-thumb { background-color: var(--neutral-600); }

.table-container {
    border-radius: 0.375rem; border: 1px solid var(--neutral-200);
    transition: var(--transition-theme); flex-grow: 1; margin-bottom: 0.2rem;
    overflow-x: auto; -webkit-overflow-scrolling: touch;
}
table { width: 100%; border-collapse: collapse; font-size: 0.875rem; table-layout: fixed; }
th {
    background-color: var(--neutral-50); font-weight: 600; text-align: left;
    padding: 0.75rem 1rem; border-bottom: 1px solid var(--neutral-200);
    white-space: nowrap;
    transition: var(--transition-theme);
}
td {
    padding: 0.5rem 1rem; border-bottom: 1px solid var(--neutral-200);
    vertical-align: middle; transition: var(--transition-theme);
}
td .form-input, td .form-select { max-width: 100%; }
tr:last-child td { border-bottom: none; }

th.th-paper-name, td.td-paper-name { width: 180px; }
th.th-source, td.td-source { width: 120px; }
th.th-sheet-h, td.td-sheet-h { width: 100px; }
th.th-sheet-w, td.td-sheet-w { width: 100px; }
th.th-grain-dir, td.td-grain-dir { width: 110px; }
th.th-caliper, td.td-caliper { width: 90px; }
th.th-cost-ream, td.td-cost-ream { width: 110px; }
th.th-gsm, td.td-gsm { width: 90px; }
th.th-cost-tonne, td.td-cost-tonne { width: 100px; }
th.th-actions, td.td-actions { width: 80px; text-align: center; }

.result-card {
    background-color: white; border-radius: 0.375rem; padding: 1rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); border: 1px solid var(--neutral-200);
    position: relative; padding-top: 1.5rem;
    transition: var(--transition-theme);
    min-height: 380px; display: flex; flex-direction: column;
}
.result-label { font-size: 0.75rem; color: var(--neutral-500); margin-bottom: 0.125rem; transition: var(--transition-theme); }
.result-value {
    font-size: 0.9rem; font-weight: 600; color: var(--neutral-800);
    word-wrap: break-word; white-space: normal; transition: var(--transition-theme);
}
.result-value-small { font-size: 0.8rem; font-weight: 500; color: var(--neutral-600); transition: var(--transition-theme); }
.result-note { font-size: 0.75rem; font-style: italic; color: var(--neutral-500); display: inline; margin-left: 0.25rem; transition: var(--transition-theme); }
.grain-aligned { color: var(--success); }
.grain-misaligned { color: var(--danger); }
.best-option { border: 2px solid var(--accent); }
.best-option-badge {
    position: absolute; top: 0; right: 0; background-color: var(--accent); color: white;
    font-size: 0.75rem; font-weight: 600; padding: 0.25rem 0.75rem;
    border-radius: 0 0.25rem 0 0.25rem; z-index: 5; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn {
    padding: 0.5rem 1rem; border-radius: 0.375rem; font-weight: 500; cursor: pointer;
    transition: var(--transition-interactive); display: inline-flex; align-items: center;
    gap: 0.25rem; font-size: 0.875rem;
}
.btn-primary { background-color: var(--primary); color: white; border: 1px solid var(--primary-dark); }
.btn-primary:hover { background-color: var(--primary-dark); }
.btn-secondary { background-color: white; color: var(--neutral-700); border: 1px solid var(--neutral-300); }
.btn-secondary:hover { background-color: var(--neutral-50); }
.btn-success { background-color: var(--success); color: white; border: 1px solid #059669; } /* Emerald-600 for border */
.btn-success:hover { background-color: #059669; }

.delete-row {
    color: #ef4444;
    background: none;
    border: none;
    padding: 0.25rem;
    cursor: pointer;
    transition: var(--transition-interactive), background-color var(--transition-duration) var(--transition-timing);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 0.25rem;
}
.delete-row:hover {
    color: #dc2626;
    background-color: rgba(239, 68, 68, 0.1);
}
.delete-row svg {
    width: 1.25rem;
    height: 1.25rem;
    pointer-events: none; /* Ensure clicks pass through to the button */
}
.delete-row svg path {
    pointer-events: none; /* Ensure clicks pass through to the button */
}
.btn svg { width: 1rem; height: 1rem; }

.lang-switch, .theme-switch, .alignment-switch, .double-lip-switch { /* Common switch base */
    position: relative; display: inline-block; cursor: pointer;
    transition: var(--transition-theme), transform var(--transition-duration) var(--transition-timing);
    overflow: hidden;
    -webkit-tap-highlight-color: transparent; user-select: none; outline: none;
}
.lang-switch:before, .theme-switch:before, .alignment-switch:before, .double-lip-switch:before {
    content: ''; position: absolute; top: -10px; left: -10px; right: -10px; bottom: -10px;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
    opacity: 0; transition: opacity var(--transition-duration) var(--transition-timing); pointer-events: none;
}
.lang-switch:hover:before, .theme-switch:hover:before, .alignment-switch:hover:before, .double-lip-switch:hover:before { opacity: 1; }

.lang-switch {
    background: #ef4444; padding: 0.25rem; border-radius: 9999px;
    width: 80px; height: 36px;
    box-shadow: 0 4px 10px rgba(239, 68, 68, 0.3), inset 0 2px 3px rgba(255, 255, 255, 0.2);
    position: relative; /* Ensure position is set */
    cursor: pointer;
}
.lang-switch.active { background: #4f46e5; box-shadow: 0 4px 10px rgba(79, 70, 229, 0.3), inset 0 2px 3px rgba(255, 255, 255, 0.2); }
.lang-switch:hover { transform: translateY(-1px); }
.lang-switch-label {
    font-size: 0.75rem; font-weight: 600; z-index: 1; position: absolute;
    top: 50%; transform: translateY(-50%); transition: var(--transition-theme);
    color: rgba(255, 255, 255, 0.7); letter-spacing: 0.5px;
}
.lang-switch-label.en { left: 15px; } .lang-switch-label.zh { right: 15px; }
.lang-switch-toggle {
    width: 30px; height: 30px; background: #ffffff; border-radius: 9999px;
    position: absolute; top: 3px; left: 3px;
    transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1); /* Keep the bouncy effect for toggle */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); z-index: 2;
}
.lang-switch-toggle:after {
    content: ''; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
    width: 16px; height: 16px; background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border-radius: 50%; opacity: 0.15; transition: opacity var(--transition-duration) var(--transition-timing);
}
.lang-switch.active .lang-switch-toggle { transform: translateX(44px); }
.lang-switch.active .lang-switch-label.en { color: rgba(255, 255, 255, 0.7); }
.lang-switch.active .lang-switch-label.zh { color: #ffffff; font-weight: 800; }
.lang-switch:not(.active) .lang-switch-label.en { color: #ffffff; font-weight: 800; }
.lang-switch:not(.active) .lang-switch-label.zh { color: rgba(255, 255, 255, 0.7); }
.lang-switch:not(.active):active .lang-switch-toggle { transform: scale(0.95); }
.lang-switch.active:active .lang-switch-toggle { transform: translateX(44px) scale(0.95); }
.lang-switch:active .lang-switch-toggle:after { opacity: 0.3; }

.theme-switch {
    background: linear-gradient(135deg, #f59e0b, #fbbf24); padding: 0.25rem; border-radius: 9999px;
    width: 80px; height: 36px;
    box-shadow: 0 4px 10px rgba(245, 158, 11, 0.3), inset 0 2px 3px rgba(255, 255, 255, 0.2);
    position: relative; /* Ensure position is set */
    cursor: pointer;
}
.theme-switch:hover {
    box-shadow: 0 6px 12px rgba(245, 158, 11, 0.4), inset 0 2px 3px rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}
.theme-switch.dark { background: linear-gradient(135deg, #1f2937, #374151); }
.theme-icon {
    position: absolute; top: 50%; transform: translateY(-50%); z-index: 1;
    transition: var(--transition-theme); color: rgba(255, 255, 255, 0.9); opacity: 1 !important;
}
.theme-icon.sun { width: 20px; height: 20px; left: 12px; }
.theme-icon.moon { width: 16px; height: 16px; right: 12px; }
.theme-switch-toggle {
    width: 30px; height: 30px; background: #ffffff; border-radius: 9999px;
    position: absolute; top: 3px; left: 3px;
    transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1); /* Keep the bouncy effect for toggle */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); z-index: 2;
    display: flex; align-items: center; justify-content: center;
}
.theme-switch-toggle:after {
    content: ''; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
    width: 16px; height: 16px; background: linear-gradient(135deg, #f59e0b, #fbbf24);
    border-radius: 50%; opacity: 0.15; transition: opacity var(--transition-duration) var(--transition-timing), background var(--transition-duration) var(--transition-timing);
}
.theme-switch.dark .theme-switch-toggle { transform: translateX(44px); }
.theme-switch.dark .theme-switch-toggle:after { background: linear-gradient(135deg, #1f2937, #374151); }
.theme-switch:not(.dark):active .theme-switch-toggle { transform: scale(0.95); }
.theme-switch.dark:active .theme-switch-toggle { transform: translateX(44px) scale(0.95); }

/* Dark Mode Styles */
body.dark-mode { background-color: var(--neutral-900); color: var(--neutral-100); }
body.dark-mode h1, body.dark-mode h2, body.dark-mode h3 { color: var(--neutral-100); }
body.dark-mode p, body.dark-mode .dark-mode-text { color: var(--neutral-300); }
body.dark-mode header { background-color: var(--neutral-800); } /* Added rule for dark mode header */
body.dark-mode .card {
    background-color: var(--neutral-800);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--neutral-700);
}
body.dark-mode .card-header {
    border-bottom-color: var(--neutral-700);
}
body.dark-mode .parameters-tabs {
    background-color: var(--neutral-800);
    border-color: var(--neutral-700);
}
body.dark-mode .parameters-tabs button[aria-selected="true"]::after {
    background-color: var(--primary-light);
}
body.dark-mode .parameters-tabs button:hover::after {
    background-color: var(--primary-light);
}
body.dark-mode .form-input, body.dark-mode .form-select {
    background-color: var(--neutral-700); border-color: var(--neutral-600); color: var(--neutral-100);
}
body.dark-mode .form-input::placeholder { color: var(--neutral-500); }
body.dark-mode .form-input:focus, body.dark-mode .form-select:focus {
    border-color: var(--primary-light); box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
    background-color: var(--neutral-600);
}
body.dark-mode .form-select {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%239ca3af'%3E%3Cpath fill-rule='evenodd' d='M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z' clip-rule='evenodd' /%3E%3C/svg%3E");
}
body.dark-mode .form-label { color: var(--neutral-300); }
body.dark-mode .table-container { border-color: var(--neutral-700); }
body.dark-mode th { background-color: var(--neutral-800); border-bottom-color: var(--neutral-700); color: var(--neutral-200); }
body.dark-mode td { border-bottom-color: var(--neutral-700); color: var(--neutral-300); }
body.dark-mode .result-card { background-color: var(--neutral-800); border-color: var(--neutral-700); box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); }
body.dark-mode .result-label { color: var(--neutral-400); }
body.dark-mode .result-value { color: var(--neutral-100); }
body.dark-mode .result-value-small { color: var(--neutral-400); }
body.dark-mode .result-note { color: var(--neutral-500); }
body.dark-mode .btn-secondary { background-color: var(--neutral-700); color: var(--neutral-200); border-color: var(--neutral-600); }
body.dark-mode .btn-secondary:hover { background-color: var(--neutral-600); }
body.dark-mode .modal-content { background-color: var(--neutral-800); box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3); }
body.dark-mode .modal-message { color: var(--neutral-300); }
body.dark-mode .delete-row { color: var(--danger); }
body.dark-mode .delete-row:hover {
    color: #ff7f7f;
    background-color: rgba(239, 68, 68, 0.15);
}
body.dark-mode .converter-divider { border-color: var(--neutral-700); }

.modal-overlay {
    position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.6);
    display: flex; align-items: center; justify-content: center; z-index: 1000;
    opacity: 0; visibility: hidden; transition: opacity var(--transition-duration) var(--transition-timing),
                                               visibility var(--transition-duration) var(--transition-timing);
}
.modal-overlay.visible { opacity: 1; visibility: visible; }
.modal-content {
    background-color: white; padding: 1.5rem 2rem; border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); max-width: 400px; text-align: center;
    transition: var(--transition-theme);
}
.modal-message { margin-bottom: 1rem; color: var(--neutral-700); transition: var(--transition-theme); }

@keyframes textFadeOut { from { opacity: 1; transform: translateY(0); } to { opacity: 0; transform: translateY(-4px); } }
@keyframes textFadeIn { from { opacity: 0; transform: translateY(4px); } to { opacity: 1; transform: translateY(0); } }
.text-transition-out { animation: textFadeOut 0.12s ease-out forwards; }
.text-transition-in { animation: textFadeIn 0.16s ease-in forwards; }
[data-translate-key] { display: inline-block; vertical-align: bottom; }
h1[data-translate-key], h2[data-translate-key], h3[data-translate-key], p[data-translate-key], label[data-translate-key], span[data-translate-key], button > span[data-translate-key] { display: inline-block; }
header h1[data-translate-key="appTitle"], header p[data-translate-key="appSubtitle"] { display: block; } /* Adjusted for new keys */
.card-header h2[data-translate-key] { display: block; }
th[data-translate-key] { display: table-cell; }
td[data-translate-key] > div { display: block; }
label.form-label[data-translate-key] { display: block; }
button[data-translate-key] > span { vertical-align: middle; }

.card-header.collapsible {
    cursor: pointer; display: flex; justify-content: space-between; align-items: center;
    padding-right: 0.75rem; transition: var(--transition-theme);
}
.card-header.collapsible:hover { background-color: var(--neutral-100); }
body.dark-mode .card-header.collapsible:hover { background-color: var(--neutral-700); }
.card-content { overflow: hidden; transition: max-height var(--transition-duration) ease-out; }
.card-content.collapsed { max-height: 0 !important; padding-top: 0; padding-bottom: 0; margin-bottom: 0; } /* Ensure full collapse */
.toggle-icon { transition: var(--transition-transform); color: var(--neutral-500); }
.toggle-icon.collapsed { transform: rotate(180deg); }

/* Ensure input containers maintain proper width */
.relative.flex-1 {
    max-width: 100%;
    min-width: 0;
}

/* Modern Tabs Styles */
button[role="tab"] {
    position: relative;
    outline: none;
    white-space: nowrap;
}

button[role="tab"]:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

button[role="tab"]::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: -1;
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.2s ease;
}

button[role="tab"]:hover::before {
    opacity: 0.05;
    background-color: var(--primary);
}

button[role="tab"][aria-selected="true"]::before {
    opacity: 0.1;
    background-color: var(--primary);
}

/* Pill tabs specific styles */
button[role="tab"].rounded-full {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

button[role="tab"].rounded-full[aria-selected="true"] {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.card-glow { box-shadow: 0 0 0 2px rgba(246, 156, 30, 0.2), 0 0 10px rgba(124, 58, 237, 0.3); transition: box-shadow 0.5s ease-out; }
@keyframes pulse-glow {
    0% { box-shadow: 0 0 0 1px rgba(246,156,30,0.2), 0 0 8px rgba(124,58,237,0.3); }
    50% { box-shadow: 0 0 0 2px rgba(241,173,79,0.3), 0 0 15px rgba(124,58,237,0.4); }
    100% { box-shadow: 0 0 0 1px rgba(246,156,30,0.2), 0 0 8px rgba(124,58,237,0.3); }
}
.pulse-glow { animation: pulse-glow 1.6s ease-in-out infinite; border-radius: 0.5rem; }
.fade-out-glow { animation: fade-out-pulse 1.5s forwards ease-out; border-radius: 0.5rem; }
@keyframes fade-out-pulse {
    0% { box-shadow: 0 0 0 1px rgba(246,156,30,0.2), 0 0 8px rgba(124,58,237,0.3); }
    100% { box-shadow: 0 0 0 0 rgba(246,156,30,0), 0 0 0 rgba(124,58,237,0); }
}

body.dark-mode .grain-aligned { color: var(--success); }
body.dark-mode .grain-misaligned { color: var(--danger); }
body.dark-mode .text-red-500, body.dark-mode .text-red-600 { color: #f87171; }
body.dark-mode .text-danger { color: #f87171; }
body.dark-mode .text-success { color: #34d399; }
body.dark-mode .text-info { color: #60a5fa; }
body.dark-mode .text-warning { color: #fbbf24; }

.alignment-switch {
    width: 60px; height: 26px; border-radius: 13px;
    background: linear-gradient(135deg, #10b981, #34d399);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(255, 255, 255, 0.3);
}
.alignment-switch-toggle {
    position: absolute; /* Added explicitly */
    top: 3px; left: 3px; width: 20px; height: 20px; border-radius: 50%;
    background: #ffffff; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1); z-index: 2;
}
.alignment-switch-toggle:after {
    content: ""; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
    width: 10px; height: 10px; border-radius: 50%;
    background: rgba(16, 185, 129, 0.2); transition: all 0.3s ease;
}
.alignment-switch.misaligned { background: linear-gradient(135deg, #ef4444, #f87171); box-shadow: 0 2px 5px rgba(239, 68, 68, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.3); }
.alignment-switch.misaligned .alignment-switch-toggle { transform: translateX(34px); }
.alignment-switch.misaligned .alignment-switch-toggle:after { background: rgba(239, 68, 68, 0.2); }
.alignment-switch:not(.misaligned):active .alignment-switch-toggle { transform: scale(0.95); }
.alignment-switch.misaligned:active .alignment-switch-toggle { transform: translateX(34px) scale(0.95); }
.alignment-mode-text { font-size: 0.875rem; transition: all 0.3s ease; margin-left: 0.5rem; }
.alignment-mode-text.aligned-text { display: none; font-weight: 600; color: #10b981; }
.alignment-mode-text.misaligned-text { display: inline-block; font-weight: 600; color: #ef4444; }
.alignment-switch:not(.misaligned) ~ span .alignment-mode-text.aligned-text { display: inline-block; }
.alignment-switch:not(.misaligned) ~ span .alignment-mode-text.misaligned-text { display: none; }
body.dark-mode .alignment-mode-text.aligned-text { color: #34d399; }
body.dark-mode .alignment-mode-text.misaligned-text { color: #f87171; }

.double-lip-switch {
    width: 48px; height: 24px; border-radius: 12px;
    background: linear-gradient(135deg, #10b981, #34d399);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(255, 255, 255, 0.3);
}
.double-lip-switch-toggle {
    position: absolute; /* Added explicitly */
    top: 3px; left: 3px; width: 18px; height: 18px; border-radius: 50%;
    background: #ffffff; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1); z-index: 2;
}
.double-lip-switch-toggle:after {
    content: ""; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
    width: 8px; height: 8px; border-radius: 50%;
    background: rgba(16, 185, 129, 0.2); transition: all 0.3s ease;
}
.double-lip-switch.active { background: linear-gradient(135deg, #ef4444, #f87171); box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2), inset 0 1px 2px rgba(255, 255, 255, 0.3); }
.double-lip-switch.active .double-lip-switch-toggle { transform: translateX(24px); }
.double-lip-switch.active .double-lip-switch-toggle:after { background: rgba(239, 68, 68, 0.2); }
.double-lip-switch:not(.active):active .double-lip-switch-toggle { transform: scale(0.95); }
.double-lip-switch.active:active .double-lip-switch-toggle { transform: translateX(24px) scale(0.95); }
.double-lip-text { font-size: 0.875rem; transition: all 0.3s ease; margin-left: 0.25rem; min-width: 1.5rem; display: inline-block; text-align: left; }
.double-lip-switch ~ .double-lip-text:before { content: "x1"; font-weight: 600; color: #10b981; }
.double-lip-switch.active ~ .double-lip-text:before { content: "x2"; font-weight: 600; color: #ef4444; }
body.dark-mode .double-lip-switch ~ .double-lip-text:before { color: #34d399; }
body.dark-mode .double-lip-switch.active ~ .double-lip-text:before { color: #f87171; }

.loading-overlay {
    position: fixed; top: 0; left: 0; right: 0; bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex; align-items: center; justify-content: center; z-index: 2000;
    opacity: 0; visibility: hidden; transition: opacity 0.2s ease, visibility 0.2s ease;
}
body.dark-mode .loading-overlay { background-color: rgba(17, 24, 39, 0.7); }
.loading-overlay.visible { opacity: 1; visibility: visible; }
.loading-spinner {
    border: 5px solid var(--neutral-300); border-top: 5px solid var(--primary);
    border-radius: 50%; width: 50px; height: 50px; animation: spin 1s linear infinite;
}
body.dark-mode .loading-spinner { border-color: var(--neutral-600); border-top-color: var(--primary-light); }
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

/* --- Tab Styles --- */
.tabs { display: flex; margin-bottom: 1.5rem; border-bottom: 2px solid var(--neutral-200); }
body.dark-mode .tabs { border-bottom-color: var(--neutral-700); }
.tab-button {
    padding: 0.75rem 1.5rem; /* Increased padding */
    cursor: pointer;
    border: none;
    background-color: transparent;
    font-size: 1rem; /* Slightly larger font */
    font-weight: 500;
    color: var(--neutral-500);
    margin-bottom: -2px; /* To align with the border-bottom of .tabs */
    border-bottom: 2px solid transparent;
    transition: color 0.2s ease, border-color 0.2s ease;
    outline: none;
    min-width: 100px; /* Add minimum width to stabilize tabs */
    text-align: center; /* Center text within the button */
}
.tab-button:hover { color: var(--primary); }
.tab-button.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
    font-weight: 600;
}
body.dark-mode .tab-button { color: var(--neutral-400); }
body.dark-mode .tab-button:hover { color: var(--primary-light); }
body.dark-mode .tab-button.active { color: var(--primary-light); border-bottom-color: var(--primary-light); }
.tab-content { display: none; }
.tab-content.active { display: block; animation: fadeIn 0.3s ease-out; }
@keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }

/* --- Selected Components Summary Panel Styles (Header Version) --- */
#summary-panel-container {
    position: relative; /* Changed from fixed */
    /* Removed bottom/right positioning */
    z-index: 1050;
    display: flex; /* Keep flex for alignment if needed */
    align-items: center; /* Align button vertically */
}
#summary-toggle-button {
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 36px; /* Match toggle height */
    height: 36px; /* Match toggle height */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15); /* Adjusted shadow */
    cursor: pointer;
    transition: var(--transition-transform), var(--transition-theme);
    border: none; /* Remove default border */
}
#summary-toggle-button:hover { transform: scale(1.1); background-color: var(--primary-dark); }
#summary-toggle-button svg { width: 18px; height: 18px; } /* Adjusted icon size */
#summary-panel {
    background-color: white;
    color: var(--neutral-800);
    border-radius: 0.5rem;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    padding: 1.5rem;
    position: fixed; /* Changed from absolute */
    top: 70px; /* To clear sticky header, adjust if header height changes */
    right: 0; /* Stick to the right edge of the viewport */
    /* Removed left, margin-left, margin-right */
    width: 320px; /* Reduced width for more compact display */
    max-height: 0; /* Initial state for animation */
    opacity: 0;
    overflow: hidden; /* Keep hidden initially for animation */
    visibility: hidden;
    transform: translateX(calc(100% + 1rem)) scale(0.95); /* Start off-screen to the right */
    transform-origin: top left; /* Animate from its top-left corner */
    transition: max-height var(--transition-duration) ease-out,
                opacity var(--transition-duration) ease-out,
                visibility 0s linear var(--transition-duration), /* Delay visibility until opacity transition fully ends */
                transform var(--transition-duration) ease-out,
                background-color var(--transition-duration) var(--transition-timing),
                color var(--transition-duration) var(--transition-timing),
                box-shadow var(--transition-duration) var(--transition-timing);
    z-index: 1040; /* Ensure it's above header, below modals */
    /* Performance optimizations to prevent flickering during scroll */
    will-change: transform, opacity, max-height;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    /* Prevent paint flashing during scroll */
    contain: paint;
    /* Force GPU acceleration */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
#summary-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    /* Prevent layout shifts and improve rendering */
    contain: layout;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}
.summary-static-title {
    /* Only prevent unwanted animations while keeping language/theme transitions */
    /* Improve rendering with hardware acceleration */
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    /* Prevent layout shifts during scroll */
    contain: content;
    /* Ensure the element has a stable position */
    position: relative;
    z-index: 1;
}
#summary-close-button {
    background: none;
    border: none;
    color: var(--neutral-500);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-theme);
}
#summary-close-button:hover {
    color: var(--neutral-700);
    background-color: var(--neutral-200);
}
body.dark-mode #summary-close-button {
    color: var(--neutral-400);
}
body.dark-mode #summary-close-button:hover {
    color: var(--neutral-200);
    background-color: var(--neutral-700);
}
#summary-panel.visible {
    max-height: calc(100vh - 70px - 1rem); /* Max height considering header and some bottom margin */
    opacity: 1;
    visibility: visible;
    transform: translateX(0) scale(1); /* Slide into view */
    transition: max-height var(--transition-duration) ease-out,
                opacity var(--transition-duration) ease-out,
                visibility 0s linear 0s, /* Make visible immediately when starting to show */
                transform var(--transition-duration) ease-out,
                background-color var(--transition-duration) var(--transition-timing),
                color var(--transition-duration) var(--transition-timing),
                box-shadow var(--transition-duration) var(--transition-timing);
    overflow-y: auto; /* Add scroll if content overflows */
    /* Additional performance optimizations for visible state */
    contain: content; /* Improve rendering performance by isolating content */
}
body.dark-mode #summary-toggle-button {
    background-color: var(--primary-light);
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
}
body.dark-mode #summary-toggle-button:hover { background-color: var(--primary); }
body.dark-mode #summary-panel {
    background-color: var(--neutral-800);
    color: var(--neutral-100);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}
.summary-item {
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px dashed var(--neutral-200);
    transition: border-color var(--transition-duration) var(--transition-timing);
    /* Performance optimizations */
    contain: content;
    will-change: transform;
    transform: translateZ(0);
}
body.dark-mode .summary-item { border-bottom-color: var(--neutral-700); }
.summary-item:last-child { margin-bottom: 0; padding-bottom: 0; border-bottom: none; }
.summary-item-label { font-size: 0.8rem; color: var(--neutral-500); transition: var(--transition-theme); }
body.dark-mode .summary-item-label { color: var(--neutral-400); }
.summary-item-value { font-weight: 500; word-break: break-all; transition: var(--transition-theme); }
.summary-total {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--neutral-300);
    transition: border-color var(--transition-duration) var(--transition-timing);
    /* Performance optimizations */
    contain: content;
    will-change: transform;
    transform: translateZ(0);
}
body.dark-mode .summary-total { border-top-color: var(--neutral-600); }
.summary-total-label { font-weight: 600; font-size: 1rem; transition: var(--transition-theme); }
.summary-total-value { font-weight: 700; font-size: 1.125rem; color: var(--primary); transition: var(--transition-theme); }
body.dark-mode .summary-total-value { color: var(--primary-light); }

/* "Select this Option" button specific style */
.btn-select-option { /* Use btn-success styling as base */
    background-color: var(--success); color: white; border: 1px solid #059669;
}
.btn-select-option:hover { background-color: #059669; }
.btn-select-option.selected {
    background-color: var(--accent); color: white; border-color: #d97706; /* Amber-600 */
}
.btn-select-option.selected:hover { background-color: #d97706; }

/* Accent button style for selected options */
.btn-accent {
    background-color: var(--accent); color: white; border: 1px solid #d97706; /* Amber-600 */
}
.btn-accent:hover { background-color: #d97706; }
}
