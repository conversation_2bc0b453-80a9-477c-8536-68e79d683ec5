'use client';

import React, { useState, useEffect } from 'react';
import JobSpecificationsCard from '@/components/features/JobSpecificationsCard';
import PaperOptionsTable from '@/components/features/PaperOptionsTable';
import ProductionParametersCard from '@/components/features/ProductionParametersCard';
import ResultsSection from '@/components/features/ResultsSection';
import UnitConverterCard from '@/components/features/UnitConverterCard';
import CoverSpecificationsCard from '@/components/features/CoverSpecificationsCard';
import CoverProductionParametersCard from '@/components/features/CoverProductionParametersCard';
import EndpapersSpecificationsCard from '@/components/features/EndpapersSpecificationsCard';
import EndpapersProductionParametersCard from '@/components/features/EndpapersProductionParametersCard';
import Header from '@/components/layout/Header';
import SummaryPanel from '@/components/features/SummaryPanel';
import DraggableCard from '@/components/ui/DraggableCard';
import { useLanguage } from '@/contexts/LanguageContext';

import { CalculationResult } from '@/utils/calculationEngine';

type ComponentType = 'innerText' | 'cover' | 'endpapers';

interface CardPosition {
  [key: string]: { x: number, y: number };
}

export default function HomePage() {
  const [activeTab, setActiveTab] = useState<ComponentType>('innerText');
  const [calculationResults, setCalculationResults] = useState<CalculationResult[]>([]);
  const [cardPositions, setCardPositions] = useState<CardPosition>({});
  const { t } = useLanguage();

  // Load saved card positions from localStorage on initial render
  useEffect(() => {
    const savedPositions = localStorage.getItem('cardPositions');
    if (savedPositions) {
      try {
        setCardPositions(JSON.parse(savedPositions));
      } catch (e) {
        console.error('Failed to parse saved card positions:', e);
      }
    }
  }, []);

  const handleTabClick = (tab: ComponentType) => {
    setActiveTab(tab);
    setCalculationResults([]); // Clear results when switching tabs
  };

  // Handle card drag end and save positions
  const handleCardDragEnd = (id: string, newPosition: { x: number, y: number }) => {
    const updatedPositions = {
      ...cardPositions,
      [id]: newPosition
    };

    setCardPositions(updatedPositions);

    // Save to localStorage
    localStorage.setItem('cardPositions', JSON.stringify(updatedPositions));
  };

  // Get position for a card if it exists
  const getCardPosition = (id: string) => {
    return cardPositions[id] || { x: 0, y: 0 };
  };

  return (
    <div className='min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100 text-neutral-800 dark:from-neutral-900 dark:to-neutral-950 dark:text-neutral-100 transition-all duration-500'>
      <Header activeTab={activeTab} onTabChange={handleTabClick} />
      <SummaryPanel />

      <main className='mx-auto max-w-7xl px-4 pt-12 pb-20 md:px-6 lg:px-8 premium-section'>
        {/* Inner Text Tab */}
        {activeTab === 'innerText' && (
          <div className='animate-fadeIn'>
            {/* Premium Top Row - Three Cards Side by Side */}
            <div className='mb-12 relative min-h-[450px] grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 animate-slideUp'>
              {/* Job Specifications Card */}
              <DraggableCard
                id="innerText-jobSpecs"
                initialPosition={getCardPosition("innerText-jobSpecs")}
                onDragEnd={handleCardDragEnd}
                className="w-full"
              >
                <JobSpecificationsCard />
              </DraggableCard>

              {/* Production Parameters Card */}
              <DraggableCard
                id="innerText-prodParams"
                initialPosition={getCardPosition("innerText-prodParams")}
                onDragEnd={handleCardDragEnd}
                className="w-full"
              >
                <ProductionParametersCard />
              </DraggableCard>

              {/* Unit Converter Card */}
              <DraggableCard
                id="innerText-unitConverter"
                initialPosition={getCardPosition("innerText-unitConverter")}
                onDragEnd={handleCardDragEnd}
                className="w-full"
              >
                <UnitConverterCard />
              </DraggableCard>
            </div>

            {/* Premium Bottom Section */}
            <div className='space-y-10'>
              <PaperOptionsTable setCalculationResults={setCalculationResults} componentType="innerText" />
              <ResultsSection results={calculationResults} />
            </div>
          </div>
        )}

        {/* Cover Tab */}
        {activeTab === 'cover' && (
          <div className='animate-fadeIn'>
            {/* Premium Top Row - Three Cards Side by Side */}
            <div className='mb-12 relative min-h-[450px] grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8'>
              {/* Cover Specifications Card */}
              <DraggableCard
                id="cover-specs"
                initialPosition={getCardPosition("cover-specs")}
                onDragEnd={handleCardDragEnd}
                className="w-full"
              >
                <CoverSpecificationsCard />
              </DraggableCard>

              {/* Cover Production Parameters Card */}
              <DraggableCard
                id="cover-prodParams"
                initialPosition={getCardPosition("cover-prodParams")}
                onDragEnd={handleCardDragEnd}
                className="w-full"
              >
                <CoverProductionParametersCard />
              </DraggableCard>

              {/* Unit Converter Card */}
              <DraggableCard
                id="cover-unitConverter"
                initialPosition={getCardPosition("cover-unitConverter")}
                onDragEnd={handleCardDragEnd}
                className="w-full"
              >
                <UnitConverterCard />
              </DraggableCard>
            </div>

            {/* Premium Bottom Section */}
            <div className='space-y-10'>
              <PaperOptionsTable setCalculationResults={setCalculationResults} componentType="cover" />
              <ResultsSection results={calculationResults} />
            </div>
          </div>
        )}

        {/* Endpapers Tab */}
        {activeTab === 'endpapers' && (
          <div className='animate-fadeIn'>
            {/* Premium Top Row - Three Cards Side by Side */}
            <div className='mb-12 relative min-h-[450px] grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8'>
              {/* Endpapers Specifications Card */}
              <DraggableCard
                id="endpapers-specs"
                initialPosition={getCardPosition("endpapers-specs")}
                onDragEnd={handleCardDragEnd}
                className="w-full"
              >
                <EndpapersSpecificationsCard />
              </DraggableCard>

              {/* Endpapers Production Parameters Card */}
              <DraggableCard
                id="endpapers-prodParams"
                initialPosition={getCardPosition("endpapers-prodParams")}
                onDragEnd={handleCardDragEnd}
                className="w-full"
              >
                <EndpapersProductionParametersCard />
              </DraggableCard>

              {/* Unit Converter Card */}
              <DraggableCard
                id="endpapers-unitConverter"
                initialPosition={getCardPosition("endpapers-unitConverter")}
                onDragEnd={handleCardDragEnd}
                className="w-full"
              >
                <UnitConverterCard />
              </DraggableCard>
            </div>

            {/* Premium Bottom Section */}
            <div className='space-y-10'>
              <PaperOptionsTable setCalculationResults={setCalculationResults} componentType="endpapers" />
              <ResultsSection results={calculationResults} />
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
