'use client';

import React, { useState, useEffect, useRef } from 'react'; // Import useState, useEffect, useRef
import { CalculationResult } from '@/utils/calculationEngine';
import ResultCard from './ResultCard';

interface ResultsSectionProps {
  results: CalculationResult[];
}

const ResultsSection: React.FC<ResultsSectionProps> = ({ results }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // Effect to manage max-height for collapse animation
  useEffect(() => {
    if (contentRef.current) {
      if (isCollapsed) {
        contentRef.current.style.maxHeight = '0';
      } else {
        // Set max-height to scrollHeight to enable the transition
        contentRef.current.style.maxHeight = contentRef.current.scrollHeight + 'px';
      }
    }
  }, [isCollapsed, results]); // Re-run effect if results change (to adjust max-height)

  // Placeholder for determining the best option - this logic will need refinement
  const getBestOptionId = (): string | number | undefined => {
    if (!results || results.length === 0) return undefined;
    const optimal = results.find(r => r.isOptimalCandidate && !r.error);
    if (optimal) return optimal.id;
    
    const sortedByCost = [...results]
        .filter(r => !r.error && r.totalCost !== undefined && r.totalCost !== Infinity)
        .sort((a, b) => (a.totalCost || Infinity) - (b.totalCost || Infinity));
    return sortedByCost.length > 0 ? sortedByCost[0].id : undefined;
  };

  const bestOptionId = getBestOptionId();

  // Only show the section if there are results
  const isSectionVisible = results && results.length > 0;

  return (
    <div id='results-section_innerText' className={`card results-section-card shadow-md ${isSectionVisible ? '' : 'hidden'}`}>
      <div className='card-header collapsible' id='results-header_innerText' onClick={() => setIsCollapsed(!isCollapsed)}>
        <div>
          <h2 className='text-xl font-semibold' data-translate-key='resultsTitle'>
            Calculation Results
          </h2>
        </div>
      </div>
      <div id='results-content_innerText' className={`card-content results-content-card ${isCollapsed ? 'collapsed' : ''}`} ref={contentRef}>
        <div id='results-container_innerText' className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 results-container-div'>
          {results && results.length > 0 ? (
            results.map((result, index) => (
              <ResultCard key={result.id || `result-${index}`} result={result} isBestOption={result.id === bestOptionId} />
            ))
          ) : (
            <p className='text-neutral-500'>No results to display. Please perform a calculation.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResultsSection;
