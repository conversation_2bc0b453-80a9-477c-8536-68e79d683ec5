'use client'; // Required for <PERSON>ust<PERSON> to work in App Router components

import React, { useEffect } from 'react';
import { useInnerTextStore, InnerTextJobSpecs } from '@/stores/innerTextStore';
import CardWrapper from '@/components/ui/CardWrapper';

const mmToIn = (mm: number | string): string => {
  const num = parseFloat(String(mm));
  return isNaN(num) ? '' : (num / 25.4).toFixed(2);
};

const inToMm = (inch: number | string): string => {
  const num = parseFloat(String(inch));
  return isNaN(num) ? '' : (num * 25.4).toFixed(1);
};

const JobSpecificationsCard = () => {
  const { jobSpecs, setJobSpec } = useInnerTextStore();

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    field: keyof InnerTextJobSpecs
  ) => {
    let value = e.target.value;
    setJobSpec(field, value);

    // Synchronize mm/inch fields
    if (field === 'trimHeightMm') {
      setJobSpec('trimHeightIn', mmToIn(value));
    } else if (field === 'trimHeightIn') {
      setJobSpec('trimHeightMm', inToMm(value));
    } else if (field === 'trimWidthMm') {
      setJobSpec('trimWidthIn', mmToIn(value));
    } else if (field === 'trimWidthIn') {
      setJobSpec('trimWidthMm', inToMm(value));
    }
  };

  // Effect to initialize inch values if mm values are present but inch values are empty
  useEffect(() => {
    if (jobSpecs.trimHeightMm && !jobSpecs.trimHeightIn) {
      setJobSpec('trimHeightIn', mmToIn(jobSpecs.trimHeightMm));
    }
    if (jobSpecs.trimWidthMm && !jobSpecs.trimWidthIn) {
      setJobSpec('trimWidthIn', mmToIn(jobSpecs.trimWidthMm));
    }
  }, [jobSpecs.trimHeightMm, jobSpecs.trimWidthMm, jobSpecs.trimHeightIn, jobSpecs.trimWidthIn, setJobSpec]);

  return (
    <CardWrapper>
      <h2 className="text-xl font-medium mb-4">Job Specifications</h2>
      
      <div className="space-y-2">
        {/* Two-column layout for height measurements */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label htmlFor="trimHeightMm" className="block text-sm mb-1">
              Page Height (mm)
            </label>
            <input
              type="number"
              id="trimHeightMm"
              className="w-full px-3 py-1 border border-gray-300 rounded"
              value={jobSpecs.trimHeightMm}
              onChange={(e) => handleInputChange(e, 'trimHeightMm')}
              step="0.1"
            />
          </div>
          <div>
            <label htmlFor="trimHeightIn" className="block text-sm mb-1">
              Page Height (in)
            </label>
            <input
              type="number"
              id="trimHeightIn"
              className="w-full px-3 py-1 border border-gray-300 rounded"
              value={jobSpecs.trimHeightIn}
              onChange={(e) => handleInputChange(e, 'trimHeightIn')}
              step="0.01"
            />
          </div>
        </div>
        
        {/* Two-column layout for width measurements */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label htmlFor="trimWidthMm" className="block text-sm mb-1">
              Page Width (mm)
            </label>
            <input
              type="number"
              id="trimWidthMm"
              className="w-full px-3 py-1 border border-gray-300 rounded"
              value={jobSpecs.trimWidthMm}
              onChange={(e) => handleInputChange(e, 'trimWidthMm')}
              step="0.1"
            />
          </div>
          <div>
            <label htmlFor="trimWidthIn" className="block text-sm mb-1">
              Page Width (in)
            </label>
            <input
              type="number"
              id="trimWidthIn"
              className="w-full px-3 py-1 border border-gray-300 rounded"
              value={jobSpecs.trimWidthIn}
              onChange={(e) => handleInputChange(e, 'trimWidthIn')}
              step="0.01"
            />
          </div>
        </div>
        
        {/* Two-column layout for pages and quantity */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label htmlFor="totalPages" className="block text-sm mb-1">
              Total Pages
            </label>
            <input
              type="number"
              id="totalPages"
              className="w-full px-3 py-1 border border-gray-300 rounded"
              value={jobSpecs.totalPages}
              onChange={(e) => handleInputChange(e, 'totalPages')}
              step="2"
            />
          </div>
          <div>
            <label htmlFor="quantity" className="block text-sm mb-1">
              Quantity
            </label>
            <input
              type="number"
              id="quantity"
              className="w-full px-3 py-1 border border-gray-300 rounded"
              value={jobSpecs.quantity}
              onChange={(e) => handleInputChange(e, 'quantity')}
            />
          </div>
        </div>
        
        {/* Full-width fields */}
        <div>
          <label htmlFor="bindingMethod" className="block text-sm mb-1">
            Binding Method
          </label>
          <select
            id="bindingMethod"
            className="w-full px-3 py-1 border border-gray-300 rounded"
            value={jobSpecs.bindingMethod}
            onChange={(e) => handleInputChange(e, 'bindingMethod')}
          >
            <option value="saddleStitch">Saddle Stitch</option>
            <option value="perfectBound">Paperback</option>
            <option value="wireO">Wire-O / Coil</option>
            <option value="sectionSewn">Section Sewn</option>
            <option value="caseBound">Case Bound (Hardcover)</option>
            <option value="singlePage">Single Page / Flat Sheet</option>
          </select>
        </div>
        
        <div>
          <label htmlFor="spoilagePercent" className="block text-sm mb-1">
            Spoilage (%)
          </label>
          <input
            type="number"
            id="spoilagePercent"
            className="w-full px-3 py-1 border border-gray-300 rounded"
            value={jobSpecs.spoilagePercent}
            onChange={(e) => handleInputChange(e, 'spoilagePercent')}
            step="0.1"
          />
        </div>
      </div>
    </CardWrapper>
  );
};

export default JobSpecificationsCard;
