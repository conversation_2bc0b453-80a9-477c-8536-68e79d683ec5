'use client';

import React from 'react';
import { CalculationResult } from '@/utils/calculationEngine'; // Assuming this path is correct

interface ResultCardProps {
  result: CalculationResult;
  isBestOption?: boolean; // To highlight the best option
  // TODO: Add translation function or context for labels
}

const ResultCard: React.FC<ResultCardProps> = ({ result, isBestOption }) => {
  const T = (key: string, fallback: string) => {
    // Basic placeholder for translation - replace with actual i18n logic
    const translations: Record<string, string> = {
      resErrorPrefix: "Error",
      na: "N/A",
      mostEfficientBadge: "Lowest Waste %",
      resInputSheet: "Input Sheet (mm)",
      resPressSize: "Press Size (mm)",
      resUsableArea: "Usable Area (mm)",
      resGrainAlignment: "Grain Alignment",
      grainAligned: "Aligned",
      grainMisaligned: "Misaligned",
      resUntrimmedPage: "Untrimmed Page (mm)",
      resImposedArea: "Imposed Area (mm)",
      resLayoutFit: "Layout Fit (Down x Across)",
      resPagesPerSide: "Pages/Side",
      resSheetUtilization: "Sheet Utilization %",
      resResultingSig: "Resulting Sig.",
      resTotalSheets: "Total Sheets",
      resCostPerSheet: "Cost/Sheet",
      resTotalCost: "Total Cost",
      resBookBlockThickness: "Book Block Thickness",
      resCostPerBook: "Cost Per Book",
      resSource: "Source",
      resInputGrain: "Input Grain",
      resGsm: "GSM",
      colCaliper: "Caliper",
    };
    return translations[key] || fallback;
  };

  const formatMmIn = (mm?: number) => {
    if (mm === undefined || isNaN(mm)) return T('na', 'N/A');
    const inches = (mm / 25.4).toFixed(2);
    return `${mm.toFixed(1)}mm (${inches}")`;
  };
  
  const grainCls = result.grainAlignmentStatus === 'Aligned' ? 'grain-aligned' : result.grainAlignmentStatus === 'Misaligned' ? 'grain-misaligned' : '';

  return (
    <div className={`result-card relative ${isBestOption ? 'best-option' : ''}`}>
      {isBestOption && <span className="best-option-badge">{T('mostEfficientBadge', 'Lowest Waste %')}</span>}
      <h3 className="mb-2 mt-1 text-lg font-semibold">
        {result.paperName} {result.error ? `(${T('resErrorPrefix', 'Error')})` : ''}
      </h3>

      {result.error && result.errorMessageKey && (
        <p className="mb-2 text-sm font-medium text-red-500 dark:text-red-400">
          {T(result.errorMessageKey, result.errorMessageKey)}
        </p>
      )}

      {/* TODO: Add pressSizeNoteKey and inputNoteKey display if present */}

      <div className="mb-3 grid grid-cols-2 gap-x-4 gap-y-3">
        <div><div className="result-label">{T('resInputSheet', 'Input Sheet (mm)')}</div><div className="result-value">{formatMmIn(result.sheetH_input)} H × {formatMmIn(result.sheetW_input)} W</div></div>
        <div><div className="result-label">{T('resPressSize', 'Press Size (mm)')}</div><div className="result-value">{formatMmIn(result.pressH)} H × {formatMmIn(result.pressW)} W</div></div>
        <div><div className="result-label">{T('resUsableArea', 'Usable Area (mm)')}</div><div className="result-value">{formatMmIn(result.usableH)} H × {formatMmIn(result.usableW)} W</div></div>
        <div><div className="result-label">{T('resGrainAlignment', 'Grain Alignment')}</div><div className={`result-value ${grainCls}`}>{result.grainAlignmentStatus || T('na', 'N/A')}</div></div>
        <div><div className="result-label">{T('resUntrimmedPage', 'Untrimmed Page (mm)')}</div><div className="result-value">{formatMmIn(result.layoutPageH)} H × {formatMmIn(result.layoutPageW)} W</div></div>
        <div><div className="result-label">{T('resImposedArea', 'Imposed Area (mm)')}</div><div className="result-value">{formatMmIn(result.occupiedHeight)} H × {formatMmIn(result.occupiedWidth)} W</div></div>
        <div><div className="result-label">{T('resLayoutFit', 'Layout Fit')}</div><div className="result-value">{result.error ? T('na', 'N/A') : `${result.winningLayoutDownPages || 0} × ${result.winningLayoutAcrossPages || 0}`}</div></div>
        <div><div className="result-label">{T('resPagesPerSide', 'Pages/Side')}</div><div className="result-value">{result.error || !result.maxItemsPerSide || result.maxItemsPerSide <= 0 ? T('na', 'N/A') : result.maxItemsPerSide}</div></div>
        <div><div className="result-label">{T('resSheetUtilization', 'Sheet Utilization %')}</div><div className="result-value">{result.error || result.maxItemsPerSide === 0 || result.wastePercent === undefined ? T('na', 'N/A') : `${((1 - result.wastePercent) * 100).toFixed(1)}%`}</div></div>
        <div><div className="result-label">{T('resResultingSig', 'Resulting Sig.')}</div><div className="result-value">{result.error || !result.pagesPerSheetOutput || result.pagesPerSheetOutput <= 0 ? T('na', 'N/A') : `${result.pagesPerSheetOutput}p`}</div></div>
        <div><div className="result-label">{T('resTotalSheets', 'Total Sheets')}</div><div className="result-value">{result.error || result.totalSheetsNeeded === undefined ? T('na', 'N/A') : result.totalSheetsNeeded.toLocaleString()}</div></div>
        <div><div className="result-label">{T('resCostPerSheet', 'Cost/Sheet')}</div><div className="result-value">{result.error || result.costPerSheet === undefined ? T('na', 'N/A') : `$${result.costPerSheet.toFixed(4)}`}</div></div>
        <div className="col-span-2"><div className="result-label">{T('resTotalCost', 'Total Cost')}</div><div className="result-value text-xl font-bold">{result.error || result.totalCost === undefined || result.totalCost === Infinity ? T('na', 'N/A') : `$${result.totalCost.toFixed(2)}`}</div></div>
      </div>
      
      <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-2 border-t border-neutral-200 pt-2 dark:border-neutral-700">
        {result.bookBlockThickness_mm !== undefined && (
            <div><div className="result-label">{T('resBookBlockThickness', 'Book Block Thickness')}</div><div className="result-value">{result.error || isNaN(result.bookBlockThickness_mm) ? T('na', 'N/A') : `${result.bookBlockThickness_mm.toFixed(2)} mm`}</div></div>
        )}
        {result.costPerBook !== undefined && (
             <div><div className="result-label">{T('resCostPerBook', 'Cost Per Book')}</div><div className="result-value">{result.error || isNaN(result.costPerBook) ? T('na', 'N/A') : `$${result.costPerBook.toFixed(4)}`}</div></div>
        )}
      </div>

      <div className="dark-mode-text mt-2 border-t border-neutral-200 pt-2 text-xs text-neutral-500 dark:border-neutral-700">
        {T('resSource', 'Source')}: {result.source === 'Pre-Cut' ? 'Pre-Cut' : 'Roll'} | {T('resInputGrain', 'Input Grain')}: {result.grainDirInput} | {T('resGsm', 'GSM')}: {result.gsm || T('na', 'N/A')}g/m² | {T('colCaliper', 'Caliper').split(' ')[0]}: {result.caliperMicrons > 0 ? `${result.caliperMicrons.toFixed(0)} µm` : T('na', 'N/A')}
      </div>

      {/* TODO: Add "Select this Option" button */}
    </div>
  );
};

export default ResultCard;
