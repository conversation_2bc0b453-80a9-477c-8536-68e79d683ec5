'use client';

import React from 'react';
import { useInnerTextStore, InnerTextProdParams } from '@/stores/innerTextStore';
import CardWrapper from '@/components/ui/CardWrapper';

const ProductionParametersCard = () => {
  const { prodParams, setProdParam, toggleDoubleLip, toggleAlignmentMode } = useInnerTextStore();

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: keyof InnerTextProdParams
  ) => {
    setProdParam(field, e.target.value);
  };

  return (
    <CardWrapper>
      <div className='card-header'>
        <h2 className='text-xl font-semibold' data-translate-key='prodParamsTitle'>
          Production Parameters
        </h2>
      </div>

      <div className="space-y-4">
        {/* Responsive grid for input groups */}
        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
          {/* Bleed */}
          <div>
            <label htmlFor="bleedMm" className="form-label" data-translate-key="bleedLabel">
              Bleed (mm)
            </label>
            <input
              type="number"
              id="bleedMm"
              className="form-input"
              value={prodParams.bleedMm}
              onChange={(e) => handleInputChange(e, 'bleedMm')}
              step="0.1"
              placeholder="e.g., 5"
            />
          </div>

          {/* Gripper */}
          <div>
            <label htmlFor="gripperMm" className="form-label" data-translate-key="gripperLabel">
              Gripper (mm)
            </label>
            <input
              type="number"
              id="gripperMm"
              className="form-input"
              value={prodParams.gripperMm}
              onChange={(e) => handleInputChange(e, 'gripperMm')}
              step="0.1"
              placeholder="e.g., 12"
            />
          </div>
        </div>

        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
          {/* Color Bar */}
          <div>
            <label htmlFor="colorBarMm" className="form-label" data-translate-key="colorBarLabel">
              Color Bar (mm)
            </label>
            <input
              type="number"
              id="colorBarMm"
              className="form-input"
              value={prodParams.colorBarMm}
              onChange={(e) => handleInputChange(e, 'colorBarMm')}
              step="0.1"
              placeholder="e.g., 6"
            />
          </div>

          {/* Side Lip */}
          <div>
            <label htmlFor="lipMm" className="form-label" data-translate-key="sideLipLabel">
              Side Lip (mm)
            </label>
            <div className="flex items-center gap-3">
              <input
                type="number"
                id="lipMm"
                className="form-input flex-1"
                value={prodParams.lipMm}
                onChange={(e) => handleInputChange(e, 'lipMm')}
                step="0.1"
                placeholder="e.g., 6"
              />
              <div
                id="double-lip-switcher_innerText"
                className={`double-lip-switch premium-switch ${prodParams.isDoubleLipActive ? 'active' : ''}`}
                title="Double Side Lip"
                onClick={toggleDoubleLip}
                role="switch"
                aria-checked={prodParams.isDoubleLipActive}
              >
                <div className="double-lip-switch-toggle"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Grain Alignment */}
        <div>
          <label className="form-label" data-translate-key="grainAlignmentLabel">
            Grain Alignment
          </label>
          <div className="flex items-center gap-3">
            <div
              id="alignment-mode-switcher_innerText"
              className={`alignment-switch premium-switch ${prodParams.alignmentMode === 'Misaligned' ? 'misaligned' : ''}`}
              title="Switch Grain Alignment"
              onClick={toggleAlignmentMode}
              role="switch"
              aria-checked={prodParams.alignmentMode === 'Aligned'}
            >
              <div className="alignment-switch-toggle"></div>
            </div>
            <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              {prodParams.alignmentMode === 'Misaligned' ? 'Misaligned' : 'Aligned'}
            </span>
          </div>
        </div>
      </div>
    </CardWrapper>
  );
};

export default ProductionParametersCard;
