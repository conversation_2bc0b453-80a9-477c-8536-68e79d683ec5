'use client';

import React from 'react';
import { useInnerTextStore, InnerTextProdParams } from '@/stores/innerTextStore';
import CardWrapper from '@/components/ui/CardWrapper';

const ProductionParametersCard = () => {
  const { prodParams, setProdParam, toggleDoubleLip, toggleAlignmentMode } = useInnerTextStore();

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: keyof InnerTextProdParams
  ) => {
    setProdParam(field, e.target.value);
  };

  return (
    <CardWrapper>
      <h2 className="text-xl font-medium mb-4">Production Parameters</h2>
      
      <div className="space-y-2">
        {/* Bleed */}
        <div>
          <label htmlFor="bleedMm" className="block text-sm mb-1">
            Bleed (mm)
          </label>
          <input
            type="number"
            id="bleedMm"
            className="w-full px-3 py-1 border border-gray-300 rounded"
            value={prodParams.bleedMm}
            onChange={(e) => handleInputChange(e, 'bleedMm')}
            step="0.1"
          />
        </div>
        
        {/* Gripper */}
        <div>
          <label htmlFor="gripperMm" className="block text-sm mb-1">
            Gripper (mm)
          </label>
          <input
            type="number"
            id="gripperMm"
            className="w-full px-3 py-1 border border-gray-300 rounded"
            value={prodParams.gripperMm}
            onChange={(e) => handleInputChange(e, 'gripperMm')}
            step="0.1"
          />
        </div>
        
        {/* Color Bar */}
        <div>
          <label htmlFor="colorBarMm" className="block text-sm mb-1">
            Color Bar (mm)
          </label>
          <input
            type="number"
            id="colorBarMm"
            className="w-full px-3 py-1 border border-gray-300 rounded"
            value={prodParams.colorBarMm}
            onChange={(e) => handleInputChange(e, 'colorBarMm')}
            step="0.1"
          />
        </div>
        
        {/* Side Lip */}
        <div>
          <label htmlFor="lipMm" className="block text-sm mb-1">
            Side Lip (mm)
          </label>
          <div className="flex items-center gap-2">
            <input
              type="number"
              id="lipMm"
              className="w-full px-3 py-1 border border-gray-300 rounded"
              value={prodParams.lipMm}
              onChange={(e) => handleInputChange(e, 'lipMm')}
              step="0.1"
            />
            <div
              id="double-lip-switcher_innerText"
              className={`double-lip-switch ${prodParams.isDoubleLipActive ? 'active' : ''}`}
              title="Double Side Lip"
              onClick={toggleDoubleLip}
              role="switch"
              aria-checked={prodParams.isDoubleLipActive}
            >
              <div className="double-lip-switch-toggle"></div>
            </div>
          </div>
        </div>
        
        {/* Grain Alignment */}
        <div>
          <label className="block text-sm mb-1">
            Grain Alignment
          </label>
          <div className="flex items-center">
            <div
              id="alignment-mode-switcher_innerText"
              className={`alignment-switch ${prodParams.alignmentMode === 'Misaligned' ? 'misaligned' : ''}`}
              title="Switch Grain Alignment"
              onClick={toggleAlignmentMode}
              role="switch"
              aria-checked={prodParams.alignmentMode === 'Aligned'}
            >
              <div className="alignment-switch-toggle"></div>
            </div>
            <span className="ml-2 text-sm">
              {prodParams.alignmentMode === 'Misaligned' ? 'Misaligned' : 'Aligned'}
            </span>
          </div>
        </div>
      </div>
    </CardWrapper>
  );
};

export default ProductionParametersCard;
